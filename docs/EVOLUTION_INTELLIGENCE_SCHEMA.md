# Evolution Intelligence Database Schema Design

## Overview

This document describes the database schema design for Memory Master v2's Evolution Intelligence Enhancement feature. The schema implements tracking and analytics for mem0's built-in evolution operations (ADD/UPDATE/DELETE/NOOP) through two primary tables: `evolution_operations` and `evolution_insights`.

## Schema Design Principles

### 1. Performance-First Design
- **Sub-50ms inserts**: Optimized for high-frequency evolution operation tracking
- **Sub-200ms analytics**: Fast aggregation queries for metrics and insights
- **Strategic indexing**: Composite indexes for common query patterns
- **Partitioning ready**: Date-based partitioning support for future scaling

### 2. Data Integrity
- **Foreign key constraints**: Proper relationships with CASCADE/SET NULL behavior
- **Enum constraints**: Type-safe operation types
- **Unique constraints**: Prevent duplicate daily insights
- **Nullable design**: Graceful handling of failed operations

### 3. Analytics Optimization
- **Pre-aggregated metrics**: Daily rollups for fast dashboard queries
- **Calculated fields**: Learning efficiency and confidence metrics
- **Time-series ready**: Optimized for trend analysis and reporting

## Table Schemas

### evolution_operations

**Purpose**: Track every individual evolution operation performed by mem0

```sql
CREATE TABLE memory_master.evolution_operations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES memory_master.users(id) ON DELETE CASCADE,
    app_id UUID NOT NULL REFERENCES memory_master.apps(id) ON DELETE CASCADE,
    memory_id UUID REFERENCES memory_master.memories(id) ON DELETE SET NULL,
    operation_type evolutionoperationtype NOT NULL,
    candidate_fact TEXT NOT NULL,
    existing_memory_content TEXT,
    similarity_score FLOAT,
    confidence_score FLOAT,
    reasoning TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'
);
```

#### Column Specifications

| Column | Type | Nullable | Description |
|--------|------|----------|-------------|
| `id` | UUID | NO | Primary key, auto-generated |
| `user_id` | UUID | NO | Foreign key to users table |
| `app_id` | UUID | NO | Foreign key to apps table |
| `memory_id` | UUID | YES | Foreign key to memories table (nullable for failed ops) |
| `operation_type` | ENUM | NO | ADD/UPDATE/DELETE/NOOP |
| `candidate_fact` | TEXT | NO | Original fact extracted from conversation |
| `existing_memory_content` | TEXT | YES | Content of existing memory (for UPDATE/DELETE) |
| `similarity_score` | FLOAT | YES | Semantic similarity score (0.0-1.0) |
| `confidence_score` | FLOAT | YES | LLM confidence in decision (0.0-1.0) |
| `reasoning` | TEXT | YES | LLM reasoning for the decision |
| `created_at` | TIMESTAMP | NO | Operation timestamp (UTC) |
| `metadata` | JSONB | YES | Additional context (request_id, chunk_info, etc.) |

#### Indexes

```sql
-- Performance-critical indexes
CREATE INDEX idx_evolution_user_date ON evolution_operations (user_id, created_at);
CREATE INDEX idx_evolution_app_operation ON evolution_operations (app_id, operation_type);
CREATE INDEX idx_evolution_memory_id ON evolution_operations (memory_id);
CREATE INDEX idx_evolution_operation_type ON evolution_operations (operation_type);

-- Standard column indexes
CREATE INDEX ix_evolution_operations_user_id ON evolution_operations (user_id);
CREATE INDEX ix_evolution_operations_app_id ON evolution_operations (app_id);
CREATE INDEX ix_evolution_operations_created_at ON evolution_operations (created_at);
```

#### Foreign Key Constraints

- `user_id → users.id` (CASCADE DELETE): Remove operations when user deleted
- `app_id → apps.id` (CASCADE DELETE): Remove operations when app deleted  
- `memory_id → memories.id` (SET NULL): Preserve operation record if memory deleted

### evolution_insights

**Purpose**: Daily aggregated metrics per user/app for fast analytics queries

```sql
CREATE TABLE memory_master.evolution_insights (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES memory_master.users(id) ON DELETE CASCADE,
    app_id UUID REFERENCES memory_master.apps(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    total_operations INTEGER NOT NULL DEFAULT 0,
    add_operations INTEGER NOT NULL DEFAULT 0,
    update_operations INTEGER NOT NULL DEFAULT 0,
    delete_operations INTEGER NOT NULL DEFAULT 0,
    noop_operations INTEGER NOT NULL DEFAULT 0,
    learning_efficiency FLOAT,
    conflict_resolution_count INTEGER NOT NULL DEFAULT 0,
    average_confidence FLOAT,
    average_similarity FLOAT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    CONSTRAINT uq_evolution_insights_user_app_date 
        UNIQUE (user_id, app_id, date)
);
```

#### Column Specifications

| Column | Type | Nullable | Description |
|--------|------|----------|-------------|
| `id` | UUID | NO | Primary key, auto-generated |
| `user_id` | UUID | NO | Foreign key to users table |
| `app_id` | UUID | YES | Foreign key to apps table (nullable for user-level aggregation) |
| `date` | DATE | NO | Aggregation date (UTC) |
| `total_operations` | INTEGER | NO | Total operations count |
| `add_operations` | INTEGER | NO | ADD operations count |
| `update_operations` | INTEGER | NO | UPDATE operations count |
| `delete_operations` | INTEGER | NO | DELETE operations count |
| `noop_operations` | INTEGER | NO | NOOP operations count |
| `learning_efficiency` | FLOAT | YES | (UPDATE + DELETE) / total_operations |
| `conflict_resolution_count` | INTEGER | NO | DELETE operations count |
| `average_confidence` | FLOAT | YES | Average confidence score for the day |
| `average_similarity` | FLOAT | YES | Average similarity score for the day |
| `created_at` | TIMESTAMP | NO | Record creation timestamp |
| `updated_at` | TIMESTAMP | NO | Last update timestamp |

#### Indexes

```sql
-- Analytics-optimized indexes
CREATE INDEX idx_insights_user_date ON evolution_insights (user_id, date);
CREATE INDEX idx_insights_app_date ON evolution_insights (app_id, date);
CREATE INDEX idx_insights_date ON evolution_insights (date);

-- Standard column indexes
CREATE INDEX ix_evolution_insights_user_id ON evolution_insights (user_id);
CREATE INDEX ix_evolution_insights_app_id ON evolution_insights (app_id);
CREATE INDEX ix_evolution_insights_created_at ON evolution_insights (created_at);
```

#### Unique Constraints

- `(user_id, app_id, date)`: Ensures one record per user/app/date combination

## Enum Types

### EvolutionOperationType

```sql
CREATE TYPE evolutionoperationtype AS ENUM (
    'ADD',      -- New memory created
    'UPDATE',   -- Existing memory enhanced/modified
    'DELETE',   -- Contradictory memory removed
    'NOOP'      -- No operation needed (redundant/irrelevant)
);
```

## Query Patterns

### High-Frequency Operations

#### 1. Insert Evolution Operation
```sql
INSERT INTO evolution_operations (
    user_id, app_id, memory_id, operation_type, 
    candidate_fact, confidence_score, reasoning
) VALUES ($1, $2, $3, $4, $5, $6, $7);
```
**Expected Performance**: <50ms

#### 2. Update Daily Insights
```sql
INSERT INTO evolution_insights (
    user_id, app_id, date, total_operations, add_operations, 
    update_operations, delete_operations, noop_operations,
    learning_efficiency, average_confidence
) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
ON CONFLICT (user_id, app_id, date) 
DO UPDATE SET 
    total_operations = evolution_insights.total_operations + EXCLUDED.total_operations,
    add_operations = evolution_insights.add_operations + EXCLUDED.add_operations,
    -- ... other fields
    updated_at = NOW();
```
**Expected Performance**: <50ms

### Analytics Queries

#### 1. User Learning Efficiency (7 days)
```sql
SELECT 
    AVG(learning_efficiency) as avg_efficiency,
    SUM(total_operations) as total_ops,
    SUM(conflict_resolution_count) as conflicts_resolved
FROM evolution_insights 
WHERE user_id = $1 
    AND date >= CURRENT_DATE - INTERVAL '7 days';
```
**Expected Performance**: <200ms

#### 2. Recent Evolution Activity
```sql
SELECT 
    operation_type, candidate_fact, confidence_score, 
    reasoning, created_at
FROM evolution_operations 
WHERE user_id = $1 
ORDER BY created_at DESC 
LIMIT 10;
```
**Expected Performance**: <100ms

#### 3. App-Level Evolution Trends
```sql
SELECT 
    date, total_operations, learning_efficiency,
    add_operations, update_operations, delete_operations
FROM evolution_insights 
WHERE app_id = $1 
    AND date >= $2 
ORDER BY date;
```
**Expected Performance**: <200ms

## Migration Strategy

### Migration File: `add_evolution_intelligence_tables.py`

#### Upgrade Process
1. Create `evolutionoperationtype` enum
2. Create `evolution_operations` table with all indexes
3. Create `evolution_insights` table with all indexes
4. Add foreign key constraints with proper CASCADE behavior
5. Add unique constraints

#### Rollback Process
1. Drop `evolution_insights` table (cascades to indexes)
2. Drop `evolution_operations` table (cascades to indexes)
3. Drop `evolutionoperationtype` enum

#### Safety Features
- **Idempotent**: Can be run multiple times safely
- **Atomic**: All operations in single transaction
- **Reversible**: Complete rollback capability
- **Non-blocking**: No locks on existing tables

## Performance Considerations

### Write Performance
- **Batch inserts**: Support for bulk operation tracking
- **Async aggregation**: Daily insights updated asynchronously
- **Minimal locking**: No locks on existing memory operations

### Read Performance
- **Covering indexes**: Reduce disk I/O for common queries
- **Partitioning ready**: Date-based partitioning for large datasets
- **Materialized views**: Future optimization for complex analytics

### Storage Optimization
- **JSONB metadata**: Efficient storage for variable context data
- **TEXT fields**: Appropriate for variable-length content
- **Nullable fields**: Reduce storage for optional data

## Monitoring and Maintenance

### Key Metrics to Monitor
- **Insert rate**: evolution_operations inserts/second
- **Table growth**: Row count and disk usage trends
- **Query performance**: P95 latency for analytics queries
- **Index usage**: Ensure indexes are being utilized

### Maintenance Tasks
- **Daily aggregation**: Automated insights calculation
- **Partition management**: Archive old evolution_operations data
- **Index maintenance**: REINDEX on high-churn tables
- **Statistics update**: ANALYZE for query optimization

## Security Considerations

### Data Privacy
- **User isolation**: All queries filtered by user_id
- **App isolation**: App-level data separation
- **Audit trail**: Complete operation history preservation

### Access Control
- **Row-level security**: Future RLS implementation ready
- **API-level filtering**: Application-layer access control
- **Sensitive data**: No PII in evolution tracking

## Future Enhancements

### Scalability
- **Horizontal partitioning**: Date-based table partitioning
- **Read replicas**: Separate analytics from operational queries
- **Archival strategy**: Move old data to cold storage

### Analytics
- **Machine learning**: Pattern recognition in evolution data
- **Predictive insights**: Forecast learning efficiency trends
- **Comparative analytics**: User vs. system averages

### Integration
- **Real-time streaming**: Event-driven evolution processing
- **External analytics**: Export to BI tools
- **API enhancements**: GraphQL for complex queries

## Conclusion

The evolution intelligence schema provides a robust foundation for tracking and analyzing mem0's evolution operations. The design prioritizes performance, data integrity, and analytics capabilities while maintaining compatibility with the existing Memory Master v2 architecture.

Key benefits:
- **High performance**: Sub-50ms inserts, sub-200ms analytics
- **Comprehensive tracking**: Complete evolution operation history
- **Rich analytics**: Learning efficiency and trend analysis
- **Scalable design**: Ready for future growth and enhancements
- **Maintainable**: Clear schema with proper constraints and indexes