#!/usr/bin/env python3
"""
Test script to validate custom prompts are working and generating UPDATE/DELETE operations.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.evolution_prompts import get_default_technical_prompts
from app.utils.memory import get_default_memory_config
import json

def test_custom_prompts_loaded():
    """Test that custom prompts are loaded correctly."""
    print("🔍 Testing custom prompts loading...")
    
    try:
        prompts = get_default_technical_prompts()
        
        print(f"   Fact extraction prompt length: {len(prompts['custom_fact_extraction_prompt'])}")
        print(f"   Update memory prompt length: {len(prompts['custom_update_memory_prompt'])}")
        
        # Check if prompts contain key evolution terms
        update_prompt = prompts['custom_update_memory_prompt']
        
        required_terms = ['ADD', 'UPDATE', 'DELETE', 'NOOP', 'evolution', 'technical']
        missing_terms = []
        
        for term in required_terms:
            if term not in update_prompt:
                missing_terms.append(term)
        
        if missing_terms:
            print(f"   ❌ Missing terms in update prompt: {missing_terms}")
            return False
        
        print("   ✅ Custom prompts loaded with all required terms")
        return True
        
    except Exception as e:
        print(f"   ❌ Error loading custom prompts: {e}")
        return False

def test_memory_config_includes_prompts():
    """Test that memory configuration includes custom prompts."""
    print("🔍 Testing memory configuration...")
    
    try:
        config = get_default_memory_config()
        
        print(f"   Config version: {config.get('version', 'Not set')}")
        
        has_fact_prompt = 'custom_fact_extraction_prompt' in config and config['custom_fact_extraction_prompt'] is not None
        has_update_prompt = 'custom_update_memory_prompt' in config and config['custom_update_memory_prompt'] is not None
        
        print(f"   Has fact extraction prompt: {has_fact_prompt}")
        print(f"   Has update memory prompt: {has_update_prompt}")
        
        if has_fact_prompt and has_update_prompt:
            print("   ✅ Memory config includes both custom prompts")
            return True
        else:
            print("   ❌ Memory config missing custom prompts")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing memory config: {e}")
        return False

def test_prompt_content_quality():
    """Test that prompts contain proper evolution logic."""
    print("🔍 Testing prompt content quality...")
    
    try:
        prompts = get_default_technical_prompts()
        update_prompt = prompts['custom_update_memory_prompt']
        
        # Check for specific evolution decision criteria
        quality_checks = [
            ('Operation types defined', 'ADD:', update_prompt),
            ('UPDATE operation defined', 'UPDATE:', update_prompt),
            ('DELETE operation defined', 'DELETE:', update_prompt),
            ('NOOP operation defined', 'NOOP:', update_prompt),
            ('Technical focus', 'technical', update_prompt.lower()),
            ('JSON output format', 'JSON', update_prompt),
            ('Decision criteria', 'DECISION CRITERIA', update_prompt),
            ('Examples provided', 'EXAMPLES', update_prompt),
        ]
        
        passed_checks = 0
        for check_name, search_term, content in quality_checks:
            if search_term in content:
                print(f"   ✅ {check_name}")
                passed_checks += 1
            else:
                print(f"   ❌ {check_name}")
        
        if passed_checks >= 6:  # Allow some flexibility
            print(f"   ✅ Prompt quality check passed ({passed_checks}/8)")
            return True
        else:
            print(f"   ❌ Prompt quality check failed ({passed_checks}/8)")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing prompt quality: {e}")
        return False

def show_prompt_samples():
    """Show samples of the custom prompts for manual inspection."""
    print("🔍 Custom prompt samples...")
    
    try:
        prompts = get_default_technical_prompts()
        
        print("\n   📝 Fact Extraction Prompt (first 200 chars):")
        print(f"   {prompts['custom_fact_extraction_prompt'][:200]}...")
        
        print("\n   📝 Update Memory Prompt (first 300 chars):")
        print(f"   {prompts['custom_update_memory_prompt'][:300]}...")
        
        # Show the operation definitions section
        update_prompt = prompts['custom_update_memory_prompt']
        if 'EVOLUTION OPERATIONS:' in update_prompt:
            start = update_prompt.find('EVOLUTION OPERATIONS:')
            end = update_prompt.find('\n\n', start)
            if end == -1:
                end = start + 500
            
            print("\n   📝 Evolution Operations Section:")
            print(f"   {update_prompt[start:end]}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error showing prompt samples: {e}")
        return False

def main():
    """Run all validation tests."""
    print("🧪 CUSTOM PROMPTS VALIDATION")
    print("=" * 50)
    
    tests = [
        ("Custom Prompts Loading", test_custom_prompts_loaded),
        ("Memory Config Integration", test_memory_config_includes_prompts),
        ("Prompt Content Quality", test_prompt_content_quality),
        ("Prompt Samples Display", show_prompt_samples),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 VALIDATION RESULTS")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status:10} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} validations passed")
    
    if passed >= 3:  # Allow some flexibility for display test
        print("🎉 Custom prompts validation successful!")
        print("\n💡 NEXT STEPS:")
        print("   1. Test with actual memory operations via MCP tools")
        print("   2. Monitor for UPDATE/DELETE operations in database")
        print("   3. Check evolution analytics for improved learning efficiency")
        return 0
    else:
        print("⚠️  Custom prompts validation failed - check configuration")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
