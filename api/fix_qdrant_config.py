#!/usr/bin/env python3
"""
Fix Qdrant Configuration

Update the vector store configuration to use localhost instead of memory-qdrant
"""

import json
import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.database import SessionLocal
from app.models import Config as ConfigModel

def fix_qdrant_config():
    """Update Qdrant configuration to use localhost."""
    db = SessionLocal()
    try:
        # Get current configuration
        config_record = db.query(ConfigModel).filter(ConfigModel.key == "main").first()
        if not config_record:
            print("❌ No configuration found")
            return False
        
        # Parse current configuration
        config = config_record.value
        print(f"Current vector store host: {config.get('vector_store', {}).get('config', {}).get('host', 'Not set')}")
        
        # Update vector store host
        if 'vector_store' in config and 'config' in config['vector_store']:
            config['vector_store']['config']['host'] = 'localhost'
            
            # Update the database record
            config_record.value = config
            db.commit()
            
            print("✅ Updated vector store host to localhost")
            return True
        else:
            print("❌ Vector store configuration not found")
            return False
            
    except Exception as e:
        print(f"❌ Error updating configuration: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = fix_qdrant_config()
    if success:
        print("🎉 Configuration updated successfully!")
    else:
        print("💥 Failed to update configuration")
