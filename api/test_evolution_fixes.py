#!/usr/bin/env python3
"""
Test script to verify evolution system fixes:
1. mem0 version upgrade (0.1.107 -> 0.1.112)
2. Analytics UUID consistency fixes
3. Custom prompts functionality
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.evolution_service import EvolutionService
from app.utils.memory import get_memory_client
from app.database.base import SessionLocal
from app.models import EvolutionOperation, EvolutionInsight
import mem0

def test_mem0_version():
    """Test that mem0 has been upgraded to the latest version."""
    print(f"🔍 Testing mem0 version...")
    print(f"   Current version: {mem0.__version__}")
    
    if mem0.__version__ >= "0.1.112":
        print("   ✅ mem0 version is up to date")
        return True
    else:
        print("   ❌ mem0 version needs upgrade")
        return False

def test_memory_client_config():
    """Test that memory client includes custom prompts."""
    print(f"🔍 Testing memory client configuration...")
    
    try:
        client = get_memory_client()
        if client is None:
            print("   ❌ Memory client initialization failed")
            return False
        
        print("   ✅ Memory client initialized successfully")
        return True
    except Exception as e:
        print(f"   ❌ Memory client error: {e}")
        return False

def test_evolution_analytics():
    """Test that evolution analytics are working correctly."""
    print(f"🔍 Testing evolution analytics...")
    
    try:
        service = EvolutionService()
        
        # Test get_evolution_metrics
        metrics = service.get_evolution_metrics("test_user", "week")
        print(f"   Metrics response: {metrics[:100]}...")
        
        if "Error" in metrics:
            print("   ❌ Evolution metrics returned error")
            return False
        
        # Test get_learning_insights
        insights = service.get_learning_insights("test_user")
        print(f"   Insights response: {insights[:100]}...")
        
        if "Error" in insights:
            print("   ❌ Learning insights returned error")
            return False
        
        print("   ✅ Evolution analytics working")
        return True
        
    except Exception as e:
        print(f"   ❌ Evolution analytics error: {e}")
        return False

def test_database_operations():
    """Test database operations and UUID consistency."""
    print(f"🔍 Testing database operations...")
    
    try:
        db = SessionLocal()
        
        # Count evolution operations
        ops_count = db.query(EvolutionOperation).count()
        print(f"   Evolution operations in DB: {ops_count}")
        
        # Count evolution insights
        insights_count = db.query(EvolutionInsight).count()
        print(f"   Evolution insights in DB: {insights_count}")
        
        db.close()
        
        if ops_count > 0:
            print("   ✅ Database has evolution data")
            return True
        else:
            print("   ⚠️  No evolution operations found in database")
            return True  # Not necessarily an error
            
    except Exception as e:
        print(f"   ❌ Database operations error: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 EVOLUTION SYSTEM FIXES VERIFICATION")
    print("=" * 50)
    
    tests = [
        ("mem0 Version", test_mem0_version),
        ("Memory Client Config", test_memory_client_config),
        ("Evolution Analytics", test_evolution_analytics),
        ("Database Operations", test_database_operations),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status:10} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All fixes verified successfully!")
        return 0
    else:
        print("⚠️  Some issues remain - check individual test results")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
