#!/usr/bin/env python3
"""
Evolution Intelligence Schema Validation Script

This script validates the evolution intelligence database schema by:
1. Checking that all required tables exist
2. Verifying column definitions and constraints
3. Confirming index creation
4. Testing basic CRUD operations

Usage:
    python validate_evolution_schema.py
"""

import sys
import os
import asyncio
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.orm import sessionmaker
from datetime import datetime, date
import uuid

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.models import (
    EvolutionOperation, EvolutionInsight, EvolutionOperationType,
    User, App, Memory
)
from app.database import get_db_url
from app.database.base import Base


class EvolutionSchemaValidator:
    def __init__(self):
        self.engine = None
        self.session = None
        self.inspector = None
        
    async def setup(self):
        """Setup database connection and inspector."""
        try:
            db_url = get_db_url()
            self.engine = create_engine(db_url)
            Session = sessionmaker(bind=self.engine)
            self.session = Session()
            self.inspector = inspect(self.engine)
            print("✅ Database connection established")
        except Exception as e:
            print(f"❌ Failed to connect to database: {e}")
            return False
        return True
    
    def validate_tables_exist(self):
        """Check that evolution tables exist."""
        print("\n🔍 Validating table existence...")
        
        required_tables = [
            'evolution_operations',
            'evolution_insights'
        ]
        
        existing_tables = self.inspector.get_table_names(schema='memory_master')
        
        for table in required_tables:
            if table in existing_tables:
                print(f"✅ Table {table} exists")
            else:
                print(f"❌ Table {table} missing")
                return False
        
        return True
    
    def validate_evolution_operations_schema(self):
        """Validate evolution_operations table schema."""
        print("\n🔍 Validating evolution_operations schema...")
        
        columns = self.inspector.get_columns('evolution_operations', schema='memory_master')
        column_names = [col['name'] for col in columns]
        
        required_columns = [
            'id', 'user_id', 'app_id', 'memory_id', 'operation_type',
            'candidate_fact', 'existing_memory_content', 'similarity_score',
            'confidence_score', 'reasoning', 'created_at', 'metadata'
        ]
        
        for col in required_columns:
            if col in column_names:
                print(f"✅ Column {col} exists")
            else:
                print(f"❌ Column {col} missing")
                return False
        
        # Check indexes
        indexes = self.inspector.get_indexes('evolution_operations', schema='memory_master')
        index_names = [idx['name'] for idx in indexes]
        
        required_indexes = [
            'idx_evolution_user_date',
            'idx_evolution_app_operation',
            'idx_evolution_memory_id',
            'idx_evolution_operation_type'
        ]
        
        for idx in required_indexes:
            if idx in index_names:
                print(f"✅ Index {idx} exists")
            else:
                print(f"❌ Index {idx} missing")
                return False
        
        return True
    
    def validate_evolution_insights_schema(self):
        """Validate evolution_insights table schema."""
        print("\n🔍 Validating evolution_insights schema...")
        
        columns = self.inspector.get_columns('evolution_insights', schema='memory_master')
        column_names = [col['name'] for col in columns]
        
        required_columns = [
            'id', 'user_id', 'app_id', 'date', 'total_operations',
            'add_operations', 'update_operations', 'delete_operations',
            'noop_operations', 'learning_efficiency', 'conflict_resolution_count',
            'average_confidence', 'average_similarity', 'created_at', 'updated_at'
        ]
        
        for col in required_columns:
            if col in column_names:
                print(f"✅ Column {col} exists")
            else:
                print(f"❌ Column {col} missing")
                return False
        
        # Check unique constraint
        unique_constraints = self.inspector.get_unique_constraints('evolution_insights', schema='memory_master')
        constraint_names = [uc['name'] for uc in unique_constraints]
        
        if 'uq_evolution_insights_user_app_date' in constraint_names:
            print("✅ Unique constraint uq_evolution_insights_user_app_date exists")
        else:
            print("❌ Unique constraint uq_evolution_insights_user_app_date missing")
            return False
        
        return True
    
    def validate_foreign_keys(self):
        """Validate foreign key constraints."""
        print("\n🔍 Validating foreign key constraints...")
        
        # Check evolution_operations foreign keys
        fks = self.inspector.get_foreign_keys('evolution_operations', schema='memory_master')
        fk_columns = [fk['constrained_columns'][0] for fk in fks]
        
        required_fks = ['user_id', 'app_id', 'memory_id']
        
        for fk in required_fks:
            if fk in fk_columns:
                print(f"✅ Foreign key {fk} exists in evolution_operations")
            else:
                print(f"❌ Foreign key {fk} missing in evolution_operations")
                return False
        
        # Check evolution_insights foreign keys
        fks = self.inspector.get_foreign_keys('evolution_insights', schema='memory_master')
        fk_columns = [fk['constrained_columns'][0] for fk in fks]
        
        required_fks = ['user_id', 'app_id']
        
        for fk in required_fks:
            if fk in fk_columns:
                print(f"✅ Foreign key {fk} exists in evolution_insights")
            else:
                print(f"❌ Foreign key {fk} missing in evolution_insights")
                return False
        
        return True
    
    def test_enum_type(self):
        """Test the EvolutionOperationType enum."""
        print("\n🔍 Testing EvolutionOperationType enum...")
        
        try:
            # Test all enum values
            for op_type in EvolutionOperationType:
                print(f"✅ Enum value {op_type.value} accessible")
            
            # Test enum in query
            result = self.session.execute(
                text("SELECT unnest(enum_range(NULL::memory_master.evolutionoperationtype))")
            ).fetchall()
            
            enum_values = [row[0] for row in result]
            expected_values = ['ADD', 'UPDATE', 'DELETE', 'NOOP']
            
            for value in expected_values:
                if value in enum_values:
                    print(f"✅ Database enum value {value} exists")
                else:
                    print(f"❌ Database enum value {value} missing")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ Enum validation failed: {e}")
            return False
    
    def test_basic_operations(self):
        """Test basic CRUD operations on evolution tables."""
        print("\n🔍 Testing basic CRUD operations...")
        
        try:
            # Create test data (this assumes test user/app exist)
            test_user_id = uuid.uuid4()
            test_app_id = uuid.uuid4()
            test_memory_id = uuid.uuid4()
            
            # Test evolution_operation insert
            evolution_op = EvolutionOperation(
                user_id=test_user_id,
                app_id=test_app_id,
                memory_id=test_memory_id,
                operation_type=EvolutionOperationType.ADD,
                candidate_fact="Test fact for validation",
                confidence_score=0.95,
                reasoning="Test reasoning",
                metadata_={"test": True}
            )
            
            self.session.add(evolution_op)
            self.session.flush()  # Get the ID without committing
            
            print(f"✅ EvolutionOperation insert successful (ID: {evolution_op.id})")
            
            # Test evolution_insights insert
            evolution_insight = EvolutionInsight(
                user_id=test_user_id,
                app_id=test_app_id,
                date=date.today(),
                total_operations=1,
                add_operations=1,
                update_operations=0,
                delete_operations=0,
                noop_operations=0,
                learning_efficiency=0.0,
                conflict_resolution_count=0,
                average_confidence=0.95
            )
            
            self.session.add(evolution_insight)
            self.session.flush()
            
            print(f"✅ EvolutionInsight insert successful (ID: {evolution_insight.id})")
            
            # Test query
            ops = self.session.query(EvolutionOperation).filter(
                EvolutionOperation.user_id == test_user_id
            ).all()
            
            if len(ops) > 0:
                print(f"✅ Query successful, found {len(ops)} operations")
            else:
                print("❌ Query returned no results")
                return False
            
            # Rollback test data
            self.session.rollback()
            print("✅ Test data rolled back successfully")
            
            return True
            
        except Exception as e:
            print(f"❌ CRUD operations test failed: {e}")
            self.session.rollback()
            return False
    
    def cleanup(self):
        """Cleanup database connections."""
        if self.session:
            self.session.close()
        if self.engine:
            self.engine.dispose()
        print("\n🧹 Database connections cleaned up")
    
    async def run_validation(self):
        """Run all validation tests."""
        print("🚀 Starting Evolution Intelligence Schema Validation")
        print("=" * 60)
        
        if not await self.setup():
            return False
        
        try:
            validations = [
                self.validate_tables_exist,
                self.validate_evolution_operations_schema,
                self.validate_evolution_insights_schema,
                self.validate_foreign_keys,
                self.test_enum_type,
                self.test_basic_operations
            ]
            
            all_passed = True
            for validation in validations:
                if not validation():
                    all_passed = False
            
            print("\n" + "=" * 60)
            if all_passed:
                print("🎉 All validation tests PASSED!")
                print("✅ Evolution Intelligence schema is ready for use")
            else:
                print("❌ Some validation tests FAILED!")
                print("🔧 Please review the migration and fix any issues")
            
            return all_passed
            
        finally:
            self.cleanup()


async def main():
    """Main validation function."""
    validator = EvolutionSchemaValidator()
    success = await validator.run_validation()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())