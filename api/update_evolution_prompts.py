#!/usr/bin/env python3
"""
Update Evolution Prompts

Update the custom prompts in the database with the fixed versions.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.database import SessionLocal
from app.models import Config as ConfigModel
from app.utils.evolution_prompts import get_default_technical_prompts

def update_evolution_prompts():
    """Update evolution prompts in database configuration."""
    db = SessionLocal()
    try:
        # Get current configuration
        config_record = db.query(ConfigModel).filter(ConfigModel.key == "main").first()
        if not config_record:
            print("❌ No configuration found")
            return False
        
        # Get updated prompts
        updated_prompts = get_default_technical_prompts()
        
        # Parse current configuration
        config = config_record.value
        
        # Update the prompts
        if 'mem0' not in config:
            config['mem0'] = {}
        
        config['mem0']['custom_fact_extraction_prompt'] = updated_prompts['custom_fact_extraction_prompt']
        config['mem0']['custom_update_memory_prompt'] = updated_prompts['custom_update_memory_prompt']
        
        # Update the database record
        config_record.value = config
        db.commit()
        
        print("✅ Updated evolution prompts in database")
        print(f"   Fact extraction prompt: {len(updated_prompts['custom_fact_extraction_prompt'])} chars")
        print(f"   Update memory prompt: {len(updated_prompts['custom_update_memory_prompt'])} chars")
        
        return True
        
    except Exception as e:
        print(f"❌ Error updating prompts: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = update_evolution_prompts()
    if success:
        print("🎉 Evolution prompts updated successfully!")
    else:
        print("💥 Failed to update evolution prompts")
