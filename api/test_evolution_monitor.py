#!/usr/bin/env python3
"""
Test script to validate evolution monitor functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.evolution_service import EvolutionService
from app.database.base import SessionLocal
from app.models import EvolutionOperation
from datetime import datetime, timezone

def test_evolution_monitor():
    """Test the evolution monitor functionality."""
    print("🔍 Testing evolution monitor...")
    
    try:
        service = EvolutionService()
        
        # Test with different parameters
        print("\n   📊 Monitor with default settings:")
        result = service.get_evolution_monitor("test_user")
        print(f"   {result}")
        
        print("\n   📊 Monitor with ADD filter:")
        result = service.get_evolution_monitor("test_user", limit=5, operation_filter="ADD")
        print(f"   {result}")
        
        print("\n   📊 Monitor with larger limit:")
        result = service.get_evolution_monitor("test_user", limit=20)
        print(f"   {result}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing evolution monitor: {e}")
        return False

def test_database_operations_direct():
    """Test database operations directly to see what data exists."""
    print("🔍 Testing database operations directly...")
    
    try:
        db = SessionLocal()
        
        # Get all operations
        operations = db.query(EvolutionOperation).order_by(EvolutionOperation.created_at.desc()).limit(10).all()
        
        print(f"   Total operations found: {len(operations)}")
        
        if operations:
            print("\n   📋 Recent operations:")
            for i, op in enumerate(operations[:5]):
                print(f"   {i+1}. {op.operation_type} - {op.created_at} - User: {op.user_id}")
                print(f"      Fact: {op.candidate_fact[:100]}...")
                print(f"      Confidence: {op.confidence_score}")
                print()
        
        # Check user distribution
        user_counts = db.query(EvolutionOperation.user_id).distinct().count()
        print(f"   Unique users with operations: {user_counts}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing database operations: {e}")
        return False

def test_time_formatting():
    """Test the time formatting functionality."""
    print("🔍 Testing time formatting...")
    
    try:
        service = EvolutionService()
        
        # Test with current time
        now = datetime.now(timezone.utc)
        result = service._format_time_ago(now)
        print(f"   Current time formatted: {result}")
        
        # Test with time 1 hour ago
        from datetime import timedelta
        hour_ago = now - timedelta(hours=1)
        result = service._format_time_ago(hour_ago)
        print(f"   1 hour ago formatted: {result}")
        
        # Test with time 1 day ago
        day_ago = now - timedelta(days=1)
        result = service._format_time_ago(day_ago)
        print(f"   1 day ago formatted: {result}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing time formatting: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 EVOLUTION MONITOR TESTING")
    print("=" * 50)
    
    tests = [
        ("Database Operations Direct", test_database_operations_direct),
        ("Time Formatting", test_time_formatting),
        ("Evolution Monitor", test_evolution_monitor),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status:10} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 Evolution monitor testing successful!")
        return 0
    else:
        print("⚠️  Some issues found - check individual test results")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
