#!/usr/bin/env python3
"""
Test Evolution Intelligence Core Logic

This script tests the Memory Master v2 Evolution Intelligence system to verify
that UPDATE/DELETE/NOOP operations are being generated correctly.
"""

import os
import sys
import time
import logging
from datetime import datetime

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.utils.memory import get_memory_client
from app.services.evolution_service import evolution_service
from app.database import SessionLocal
from app.models import User, App

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_test_user_and_app():
    """Create test user and app for evolution testing."""
    db = SessionLocal()
    try:
        # Create or get test user
        test_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not test_user:
            test_user = User(
                user_id="evolution_test_user",
                email="<EMAIL>",
                name="Evolution Test User"
            )
            db.add(test_user)
            db.commit()
            db.refresh(test_user)
        
        # Create or get test app
        test_app = db.query(App).filter(
            App.owner_id == test_user.id,
            App.name == "evolution_test"
        ).first()
        if not test_app:
            test_app = App(
                name="evolution_test",
                owner_id=test_user.id,
                description="Test app for evolution intelligence"
            )
            db.add(test_app)
            db.commit()
            db.refresh(test_app)
        
        return str(test_user.id), str(test_app.id)
    finally:
        db.close()

def test_memory_client_configuration():
    """Test that memory client is properly configured with custom prompts."""
    print("\n🔍 Testing Memory Client Configuration...")
    
    try:
        client = get_memory_client()
        if client is None:
            print("   ❌ Memory client initialization failed")
            return False
        
        print("   ✅ Memory client initialized successfully")
        
        # Check if client has custom prompts configured
        # This is internal to mem0, so we can't directly check, but we can verify the config was loaded
        print("   ✅ Custom prompts should be loaded from database configuration")
        return True
        
    except Exception as e:
        print(f"   ❌ Memory client error: {e}")
        return False

def test_skill_progression_scenario():
    """Test Scenario A: Skill Progression (Should trigger UPDATE)."""
    print("\n🧪 Test A: Skill Progression (Should trigger UPDATE)")
    
    user_id, app_id = setup_test_user_and_app()
    client = get_memory_client()
    
    try:
        # Step 1: Add initial learning memory
        print("   Step 1: Adding initial learning memory...")
        initial_text = "I'm learning Python programming for data analysis"
        response1 = client.add(initial_text, user_id=user_id)
        print(f"   Response 1: {response1}")
        
        # Wait a moment
        time.sleep(2)
        
        # Step 2: Add progression memory
        print("   Step 2: Adding skill progression memory...")
        progression_text = "I'm now proficient in Python with 6 months experience building data pipelines"
        response2 = client.add(progression_text, user_id=user_id)
        print(f"   Response 2: {response2}")
        
        # Analyze responses for evolution operations
        print("   Analyzing evolution operations...")
        
        # Check if response2 contains UPDATE operations
        if isinstance(response2, dict) and 'results' in response2:
            update_found = False
            for result in response2['results']:
                if isinstance(result, dict) and result.get('event') == 'UPDATE':
                    update_found = True
                    print(f"   ✅ UPDATE operation found: {result}")
                    break
            
            if not update_found:
                print("   ⚠️  No UPDATE operation found in response")
                print(f"   Events found: {[r.get('event') for r in response2['results'] if isinstance(r, dict)]}")
        else:
            print(f"   ⚠️  Unexpected response format: {type(response2)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def test_technology_migration_scenario():
    """Test Scenario B: Technology Migration (Should trigger DELETE + ADD)."""
    print("\n🧪 Test B: Technology Migration (Should trigger DELETE + ADD)")
    
    user_id, app_id = setup_test_user_and_app()
    client = get_memory_client()
    
    try:
        # Step 1: Add initial preference
        print("   Step 1: Adding initial database preference...")
        initial_text = "I prefer MySQL database for web development"
        response1 = client.add(initial_text, user_id=user_id)
        print(f"   Response 1: {response1}")
        
        # Wait a moment
        time.sleep(2)
        
        # Step 2: Add migration memory
        print("   Step 2: Adding technology migration memory...")
        migration_text = "Switched to PostgreSQL for better JSON support. No longer using MySQL"
        response2 = client.add(migration_text, user_id=user_id)
        print(f"   Response 2: {response2}")
        
        # Analyze responses for evolution operations
        print("   Analyzing evolution operations...")
        
        if isinstance(response2, dict) and 'results' in response2:
            operations_found = []
            for result in response2['results']:
                if isinstance(result, dict) and 'event' in result:
                    operations_found.append(result['event'])
            
            print(f"   Operations found: {operations_found}")
            
            if 'DELETE' in operations_found or 'UPDATE' in operations_found:
                print("   ✅ Evolution operation found (DELETE or UPDATE)")
            else:
                print("   ⚠️  No DELETE or UPDATE operations found")
        else:
            print(f"   ⚠️  Unexpected response format: {type(response2)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def test_redundant_information_scenario():
    """Test Scenario C: Redundant Information (Should trigger NOOP)."""
    print("\n🧪 Test C: Redundant Information (Should trigger NOOP)")
    
    user_id, app_id = setup_test_user_and_app()
    client = get_memory_client()
    
    try:
        # Step 1: Add specific memory
        print("   Step 1: Adding specific framework preference...")
        initial_text = "I use React for frontend development"
        response1 = client.add(initial_text, user_id=user_id)
        print(f"   Response 1: {response1}")
        
        # Wait a moment
        time.sleep(2)
        
        # Step 2: Add redundant information
        print("   Step 2: Adding redundant information...")
        redundant_text = "React is my frontend framework of choice"
        response2 = client.add(redundant_text, user_id=user_id)
        print(f"   Response 2: {response2}")
        
        # Analyze responses for evolution operations
        print("   Analyzing evolution operations...")
        
        if isinstance(response2, dict) and 'results' in response2:
            operations_found = []
            for result in response2['results']:
                if isinstance(result, dict) and 'event' in result:
                    operations_found.append(result['event'])
            
            print(f"   Operations found: {operations_found}")
            
            if 'NOOP' in operations_found or 'NONE' in operations_found or len(operations_found) == 0:
                print("   ✅ NOOP or no operations found (redundancy detected)")
            else:
                print("   ⚠️  Expected NOOP but found other operations")
        else:
            print(f"   ⚠️  Unexpected response format: {type(response2)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        import traceback
        print(f"   Traceback: {traceback.format_exc()}")
        return False

def check_evolution_operations_in_database():
    """Check the evolution operations stored in the database."""
    print("\n📊 Checking Evolution Operations in Database...")
    
    try:
        from app.database import SessionLocal
        from app.models import EvolutionOperation
        
        db = SessionLocal()
        
        # Get recent operations
        recent_ops = db.query(EvolutionOperation).order_by(
            EvolutionOperation.created_at.desc()
        ).limit(10).all()
        
        print(f"   Found {len(recent_ops)} recent evolution operations:")
        
        operation_counts = {}
        for op in recent_ops:
            op_type = op.operation_type
            operation_counts[op_type] = operation_counts.get(op_type, 0) + 1
            print(f"   - {op.operation_type}: {op.reasoning} (confidence: {op.confidence_score})")
        
        print(f"\n   Operation Distribution: {operation_counts}")
        
        # Check for evolution intelligence success
        non_add_ops = sum(count for op_type, count in operation_counts.items() if op_type != 'ADD')
        total_ops = sum(operation_counts.values())
        
        if total_ops > 0:
            evolution_percentage = (non_add_ops / total_ops) * 100
            print(f"   Evolution Intelligence: {evolution_percentage:.1f}% ({non_add_ops}/{total_ops} non-ADD operations)")
            
            if evolution_percentage > 0:
                print("   ✅ Evolution Intelligence is working!")
            else:
                print("   ❌ Evolution Intelligence not working - all operations are ADD")
        else:
            print("   ⚠️  No operations found")
        
        db.close()
        return operation_counts
        
    except Exception as e:
        print(f"   ❌ Database check failed: {e}")
        return {}

def main():
    """Run all evolution intelligence tests."""
    print("🚀 Memory Master v2 Evolution Intelligence Test Suite")
    print("=" * 60)
    
    # Test 1: Memory client configuration
    config_success = test_memory_client_configuration()
    
    # Test 2: Skill progression scenario
    skill_success = test_skill_progression_scenario()
    
    # Test 3: Technology migration scenario
    migration_success = test_technology_migration_scenario()
    
    # Test 4: Redundant information scenario
    redundancy_success = test_redundant_information_scenario()
    
    # Test 5: Check database operations
    operation_counts = check_evolution_operations_in_database()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print(f"   Memory Client Configuration: {'✅ PASS' if config_success else '❌ FAIL'}")
    print(f"   Skill Progression Test: {'✅ PASS' if skill_success else '❌ FAIL'}")
    print(f"   Technology Migration Test: {'✅ PASS' if migration_success else '❌ FAIL'}")
    print(f"   Redundancy Test: {'✅ PASS' if redundancy_success else '❌ FAIL'}")
    
    # Evolution intelligence assessment
    non_add_count = sum(count for op_type, count in operation_counts.items() if op_type != 'ADD')
    total_count = sum(operation_counts.values())
    
    if total_count > 0:
        evolution_rate = (non_add_count / total_count) * 100
        print(f"\n🧠 Evolution Intelligence Status:")
        print(f"   Learning Efficiency: {evolution_rate:.1f}%")
        print(f"   Operation Distribution: {operation_counts}")
        
        if evolution_rate > 40:
            print("   🎉 SUCCESS: Evolution Intelligence is working excellently!")
        elif evolution_rate > 10:
            print("   ⚠️  PARTIAL: Evolution Intelligence is partially working")
        else:
            print("   ❌ FAILED: Evolution Intelligence is not working properly")
    else:
        print("\n❌ No operations found - unable to assess evolution intelligence")

if __name__ == "__main__":
    main()
