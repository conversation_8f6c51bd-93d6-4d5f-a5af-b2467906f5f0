#!/usr/bin/env python3
"""
Test OpenAI API connectivity
"""

import os
import requests
from dotenv import load_dotenv

load_dotenv()

def test_openai_api():
    """Test OpenAI API connectivity."""
    api_key = os.getenv('OPENAI_API_KEY')
    
    if not api_key:
        print("✗ OPENAI_API_KEY not found in environment")
        return False
    
    print(f"✓ OPENAI_API_KEY found: {api_key[:8]}...{api_key[-4:]}")
    
    # Test API connectivity
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    try:
        print("Testing OpenAI API connectivity...")
        response = requests.get('https://api.openai.com/v1/models', headers=headers, timeout=10)
        
        if response.status_code == 200:
            models = response.json()
            model_ids = [model['id'] for model in models.get('data', [])]
            
            print("✓ OpenAI API is accessible")
            print(f"  Found {len(model_ids)} models")
            
            # Check for required models
            required_models = ['gpt-4o-mini', 'text-embedding-3-small']
            for model in required_models:
                if any(model in model_id for model_id in model_ids):
                    print(f"  ✓ {model} available")
                else:
                    print(f"  ⚠ {model} not found (might be available with different name)")
            
            return True
        else:
            print(f"✗ OpenAI API returned status {response.status_code}")
            print(f"  Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("✗ OpenAI API request timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("✗ Failed to connect to OpenAI API")
        return False
    except Exception as e:
        print(f"✗ OpenAI API test failed: {e}")
        return False

if __name__ == "__main__":
    test_openai_api()
