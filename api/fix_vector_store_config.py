#!/usr/bin/env python3
"""
Fix Vector Store Configuration Script

This script updates the database configuration to include:
1. Vector store configuration for Qdrant
2. Custom prompts for technical domain
3. Proper version settings
"""

import os
import sys
import json
from pathlib import Path

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.database import SessionLocal
from app.models import Config as ConfigModel
from app.utils.evolution_prompts import get_default_technical_prompts

def get_current_config():
    """Get current configuration from database."""
    db = SessionLocal()
    try:
        db_config = db.query(ConfigModel).filter(ConfigModel.key == "main").first()
        if db_config and db_config.value:
            return db_config.value
        return None
    finally:
        db.close()

def update_config_with_vector_store_and_prompts():
    """Update configuration to include vector store and custom prompts."""
    db = SessionLocal()
    try:
        # Get current config or create new one
        db_config = db.query(ConfigModel).filter(ConfigModel.key == "main").first()
        
        if db_config and db_config.value:
            config = db_config.value.copy()
            print("Found existing configuration, updating...")
        else:
            print("No existing configuration found, creating new one...")
            config = {
                "openmemory": {
                    "custom_instructions": None,
                    "max_text_length": 2000
                },
                "mem0": {
                    "version": "v1.1",
                    "llm": {
                        "provider": "openai",
                        "config": {
                            "model": "gpt-4o-mini",
                            "temperature": 0.1,
                            "max_tokens": 2000,
                            "api_key": "env:OPENAI_API_KEY"
                        }
                    },
                    "embedder": {
                        "provider": "openai",
                        "config": {
                            "model": "text-embedding-3-small",
                            "api_key": "env:OPENAI_API_KEY"
                        }
                    }
                }
            }
        
        # Ensure mem0 section exists
        if "mem0" not in config:
            config["mem0"] = {}
        
        # Add vector store configuration
        config["vector_store"] = {
            "provider": "qdrant",
            "config": {
                "collection_name": "openmemory",
                "host": "localhost",
                "port": 6333
            }
        }
        
        # Add custom prompts
        technical_prompts = get_default_technical_prompts()
        config["mem0"]["custom_fact_extraction_prompt"] = technical_prompts["custom_fact_extraction_prompt"]
        config["mem0"]["custom_update_memory_prompt"] = technical_prompts["custom_update_memory_prompt"]
        
        # Ensure version is set
        config["mem0"]["version"] = "v1.1"
        
        # Save updated configuration
        if db_config:
            db_config.value = config
        else:
            db_config = ConfigModel(key="main", value=config)
            db.add(db_config)
        
        db.commit()
        db.refresh(db_config)
        
        print("✓ Configuration updated successfully!")
        return config
        
    except Exception as e:
        print(f"✗ Failed to update configuration: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def verify_configuration():
    """Verify the updated configuration."""
    print("\n=== Verifying Updated Configuration ===")
    
    config = get_current_config()
    if not config:
        print("✗ No configuration found!")
        return False
    
    # Check vector store
    vector_store = config.get("vector_store")
    if vector_store and vector_store.get("provider") == "qdrant":
        print("✓ Vector store configuration present")
        print(f"  Provider: {vector_store['provider']}")
        print(f"  Host: {vector_store['config']['host']}")
        print(f"  Port: {vector_store['config']['port']}")
        print(f"  Collection: {vector_store['config']['collection_name']}")
    else:
        print("✗ Vector store configuration missing or invalid")
        return False
    
    # Check custom prompts
    mem0_config = config.get("mem0", {})
    fact_prompt = mem0_config.get("custom_fact_extraction_prompt")
    update_prompt = mem0_config.get("custom_update_memory_prompt")
    
    if fact_prompt and update_prompt:
        print("✓ Custom prompts present")
        print(f"  Fact extraction prompt: {len(fact_prompt)} characters")
        print(f"  Update memory prompt: {len(update_prompt)} characters")
    else:
        print("✗ Custom prompts missing")
        return False
    
    # Check version
    version = mem0_config.get("version")
    if version == "v1.1":
        print("✓ Version set correctly")
    else:
        print(f"⚠ Version is {version}, expected v1.1")
    
    return True

def test_memory_client_after_update():
    """Test memory client initialization after configuration update."""
    print("\n=== Testing Memory Client After Update ===")
    
    try:
        from app.utils.memory import reset_memory_client, get_memory_client
        
        # Reset the memory client to pick up new configuration
        print("Resetting memory client...")
        reset_memory_client()
        
        # Try to initialize with new configuration
        print("Initializing memory client with updated configuration...")
        client = get_memory_client()
        
        if client is None:
            print("✗ Memory client initialization returned None")
            return False
        
        print("✓ Memory client initialized successfully")
        
        # Test basic operations
        print("Testing basic operations...")
        
        # Test add operation
        test_memory = "Configuration test memory - vector store connectivity check"
        result = client.add(test_memory, user_id="config_test_user")
        
        if result and result.get('id'):
            print(f"✓ Add operation successful: {result.get('id')}")
            
            # Test search operation
            search_results = client.search("configuration test", user_id="config_test_user", limit=1)
            print(f"✓ Search operation successful: found {len(search_results)} results")
            
            # Clean up
            try:
                client.delete(result['id'])
                print("✓ Cleanup successful")
            except Exception as e:
                print(f"⚠ Cleanup failed: {e}")
            
            return True
        else:
            print("✗ Add operation failed or returned invalid result")
            return False
            
    except Exception as e:
        print(f"✗ Memory client test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to fix vector store configuration."""
    print("Vector Store Configuration Fix Tool")
    print("=" * 50)
    
    try:
        # Show current configuration
        print("Current configuration:")
        current_config = get_current_config()
        if current_config:
            print(f"  Has vector_store: {'vector_store' in current_config}")
            print(f"  Has custom prompts: {'custom_fact_extraction_prompt' in current_config.get('mem0', {})}")
        else:
            print("  No configuration found")
        
        # Update configuration
        print("\nUpdating configuration...")
        updated_config = update_config_with_vector_store_and_prompts()
        
        # Verify configuration
        if verify_configuration():
            print("\n✓ Configuration verification passed!")
        else:
            print("\n✗ Configuration verification failed!")
            return 1
        
        # Test memory client
        if test_memory_client_after_update():
            print("\n✓ Memory client test passed!")
            print("\n🎉 Vector store configuration fix completed successfully!")
            return 0
        else:
            print("\n✗ Memory client test failed!")
            return 1
            
    except Exception as e:
        print(f"\n✗ Configuration fix failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
