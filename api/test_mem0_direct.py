#!/usr/bin/env python3
"""
Direct mem0 Test

Test mem0 directly without the singleton to verify evolution intelligence.
"""

import os
import sys
import json
import logging

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from mem0 import Memory
from app.database import SessionLocal
from app.models import Config as ConfigModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_config_from_database():
    """Get configuration from database."""
    db = SessionLocal()
    try:
        config_record = db.query(ConfigModel).filter(ConfigModel.key == "main").first()
        if config_record:
            return config_record.value
        return None
    finally:
        db.close()

def create_mem0_config():
    """Create mem0 configuration with custom prompts."""

    # Get database configuration - force fresh read
    db_config = get_config_from_database()
    if not db_config:
        print("❌ No database configuration found")
        return None

    # Extract mem0 configuration
    mem0_config = db_config.get('mem0', {})

    # Debug: Print the actual prompts to verify they're updated
    custom_update_prompt = mem0_config.get('custom_update_memory_prompt', '')
    if 'existing-memory-id' in custom_update_prompt:
        print("⚠️  WARNING: Still using old prompt with 'existing-memory-id'")
    else:
        print("✅ Using updated prompt with numeric IDs")
    
    # Build configuration
    config = {
        "vector_store": {
            "provider": "qdrant",
            "config": {
                "collection_name": "openmemory",
                "host": "localhost",  # Use localhost directly
                "port": 6333
            }
        },
        "llm": mem0_config.get('llm', {
            "provider": "openai",
            "config": {
                "model": "gpt-4o-mini",
                "temperature": 0.1,
                "max_tokens": 2000,
                "api_key": os.getenv("OPENAI_API_KEY")
            }
        }),
        "embedder": mem0_config.get('embedder', {
            "provider": "openai",
            "config": {
                "model": "text-embedding-3-small",
                "api_key": os.getenv("OPENAI_API_KEY")
            }
        }),
        "version": "v1.1"
    }
    
    # Add custom prompts
    if "custom_fact_extraction_prompt" in mem0_config:
        config["custom_fact_extraction_prompt"] = mem0_config["custom_fact_extraction_prompt"]
        print(f"✅ Added custom fact extraction prompt ({len(mem0_config['custom_fact_extraction_prompt'])} chars)")
    
    if "custom_update_memory_prompt" in mem0_config:
        config["custom_update_memory_prompt"] = mem0_config["custom_update_memory_prompt"]
        print(f"✅ Added custom update memory prompt ({len(mem0_config['custom_update_memory_prompt'])} chars)")
    
    # Parse environment variables in LLM config
    if config["llm"]["config"]["api_key"] == "env:OPENAI_API_KEY":
        config["llm"]["config"]["api_key"] = os.getenv("OPENAI_API_KEY")
    
    if config["embedder"]["config"]["api_key"] == "env:OPENAI_API_KEY":
        config["embedder"]["config"]["api_key"] = os.getenv("OPENAI_API_KEY")
    
    return config

def test_mem0_evolution_intelligence():
    """Test mem0 evolution intelligence directly."""
    print("🧠 Testing mem0 Evolution Intelligence Directly")
    print("=" * 50)
    
    # Create configuration
    config = create_mem0_config()
    if not config:
        print("❌ Failed to create configuration")
        return False
    
    print(f"Configuration keys: {list(config.keys())}")
    print(f"Custom prompts included: {'custom_fact_extraction_prompt' in config and 'custom_update_memory_prompt' in config}")
    
    try:
        # Initialize mem0 client directly
        print("\n🔧 Initializing mem0 client...")
        client = Memory.from_config(config_dict=config)
        print("✅ mem0 client initialized successfully")
        
        # Test user ID
        test_user_id = "evolution_test_user"
        
        # Test 1: Add initial memory
        print("\n📝 Test 1: Adding initial memory...")
        initial_text = "I'm learning Python programming for data analysis"
        response1 = client.add(initial_text, user_id=test_user_id)
        print(f"Response 1 type: {type(response1)}")
        print(f"Response 1: {response1}")
        
        # Analyze response structure
        if isinstance(response1, dict):
            print(f"Response 1 keys: {list(response1.keys())}")
            if 'results' in response1:
                print(f"Results count: {len(response1['results'])}")
                for i, result in enumerate(response1['results']):
                    print(f"  Result {i}: {result}")
        
        # Test 2: Add progression memory (should trigger UPDATE)
        print("\n📝 Test 2: Adding skill progression memory...")
        progression_text = "I'm now proficient in Python with 6 months experience building data pipelines"
        response2 = client.add(progression_text, user_id=test_user_id)
        print(f"Response 2 type: {type(response2)}")
        print(f"Response 2: {response2}")
        
        # Analyze response structure
        if isinstance(response2, dict):
            print(f"Response 2 keys: {list(response2.keys())}")
            if 'results' in response2:
                print(f"Results count: {len(response2['results'])}")
                evolution_operations = []
                for i, result in enumerate(response2['results']):
                    print(f"  Result {i}: {result}")
                    if isinstance(result, dict) and 'event' in result:
                        evolution_operations.append(result['event'])
                
                print(f"\n🎯 Evolution Operations Found: {evolution_operations}")
                
                if any(op in ['UPDATE', 'DELETE', 'NOOP'] for op in evolution_operations):
                    print("🎉 SUCCESS: Evolution intelligence is working!")
                    return True
                else:
                    print("⚠️  Only ADD operations found - evolution intelligence may not be working")
                    return False
        
        return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def test_qdrant_connection():
    """Test Qdrant connection."""
    print("\n🔍 Testing Qdrant Connection...")
    
    try:
        import requests
        response = requests.get('http://localhost:6333/collections')
        print(f"✅ Qdrant connection successful: {response.status_code}")
        
        # Check if openmemory collection exists
        collections = response.json()
        collection_names = [col['name'] for col in collections.get('result', {}).get('collections', [])]
        print(f"Collections: {collection_names}")
        
        if 'openmemory' in collection_names:
            print("✅ openmemory collection exists")
        else:
            print("⚠️  openmemory collection not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Qdrant connection failed: {e}")
        return False

def main():
    """Run direct mem0 test."""
    print("🚀 Direct mem0 Evolution Intelligence Test")
    print("=" * 60)
    
    # Test Qdrant connection first
    qdrant_ok = test_qdrant_connection()
    if not qdrant_ok:
        print("❌ Qdrant connection failed - cannot proceed")
        return
    
    # Test evolution intelligence
    success = test_mem0_evolution_intelligence()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Evolution Intelligence Test: SUCCESS")
    else:
        print("❌ Evolution Intelligence Test: FAILED")

if __name__ == "__main__":
    main()
