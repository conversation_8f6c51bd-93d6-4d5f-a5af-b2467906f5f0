#!/usr/bin/env python3
"""
Create Better Evolution Prompt

Create a better custom update memory prompt that handles empty memory correctly.
"""

import sys
import os
from sqlalchemy import text

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app'))

from app.database import SessionLocal

# Better prompt that handles empty memory correctly
BETTER_UPDATE_MEMORY_PROMPT = """
You are an expert technical memory evolution system. Your job is to intelligently manage a developer's technical memory by deciding how to handle new information against existing memories.

EVOLUTION OPERATIONS:
- ADD: Store completely new technical information
- UPDATE: Enhance or refine existing technical knowledge  
- DELETE: Remove outdated or contradicted information
- NOOP: Ignore redundant or non-valuable information

TECHNICAL EVOLUTION RULES:

1. TECHNOLOGY MIGRATIONS:
   - "switched from X to Y" → DELETE X preference, ADD Y preference
   - "migrated from X to Y" → UPDATE to reflect migration
   - "no longer using X" → DELETE X if it was a preference

2. SKILL PROGRESSION:
   - "learning X" → "proficient in X" → UPDATE skill level
   - "beginner" → "intermediate" → "expert" → UPDATE progression
   - New certifications or achievements → ADD or UPDATE

3. PROJECT EVOLUTION:
   - "working on X" → "completed X" → UPDATE project status
   - New project started → ADD project information
   - Project cancelled → UPDATE or DELETE as appropriate

4. PREFERENCE CHANGES:
   - "prefers X over Y" when Y was previously preferred → DELETE Y preference, ADD X preference
   - Tool/framework preferences → UPDATE existing or ADD new

5. CAPABILITY UPDATES:
   - "doesn't know X" → "proficient in X" → UPDATE capability
   - New skills acquired → ADD new capabilities
   - Skills no longer relevant → DELETE if outdated

6. CONFLICT RESOLUTION:
   - Contradictory information → DELETE old, ADD new
   - Version updates → UPDATE to latest version
   - Deprecated technologies → UPDATE to mark as deprecated

DECISION CRITERIA:
- Prioritize recent, specific information over old, general information
- Maintain skill progression history when possible
- Preserve valuable context while updating facts
- Remove truly obsolete information to prevent confusion

OUTPUT FORMAT:
Return a JSON array of operations. Each operation should have:
- "id": existing memory ID (for UPDATE/DELETE) or null (for ADD)
- "text": the new or updated memory text
- "event": "ADD", "UPDATE", "DELETE", or "NOOP"
- "old_memory": existing memory text (for UPDATE/DELETE operations)

IMPORTANT RULES:
1. If no existing memories are provided, ALL operations should be ADD with "id": null
2. Only use existing memory IDs from the provided old memory list
3. Never generate new IDs - use null for ADD operations
4. For UPDATE/DELETE, use the exact ID from existing memory

EXAMPLES:

Example 1 - No existing memories:
Old Memory: []
Retrieved facts: ["Learning Python programming"]
Output: [{
  "id": null,
  "text": "Learning Python programming",
  "event": "ADD",
  "old_memory": null
}]

Example 2 - With existing memories (skill progression):
Old Memory: [{"id": "mem_123", "text": "Learning Python programming"}]
Retrieved facts: ["Expert in Python with 3 years experience"]
Output: [{
  "id": "mem_123",
  "text": "Expert in Python programming with 3 years of development experience",
  "event": "UPDATE",
  "old_memory": "Learning Python programming"
}]

Example 3 - Technology migration:
Old Memory: [{"id": "mem_456", "text": "Prefers MySQL database"}]
Retrieved facts: ["Switched to PostgreSQL for better JSON support"]
Output: [{
  "id": "mem_456",
  "text": "Prefers PostgreSQL database for better JSON support",
  "event": "UPDATE",
  "old_memory": "Prefers MySQL database"
}]

Example 4 - Redundant information:
Old Memory: [{"id": "mem_789", "text": "Uses React for frontend"}]
Retrieved facts: ["React is my frontend framework"]
Output: [{
  "id": "mem_789",
  "text": "Uses React for frontend",
  "event": "NOOP",
  "old_memory": "Uses React for frontend"
}]

Now process the following candidate fact against existing memories:
""".strip()

def update_better_prompt():
    """Update the database with the better prompt."""
    print("🔧 Updating with better evolution prompt...")
    
    db = SessionLocal()
    try:
        # Use SQL to update the JSON field
        sql_update = text("""
        UPDATE memory_master.configs 
        SET value = value::jsonb || jsonb_build_object(
            'mem0', 
            (value::jsonb->'mem0') || jsonb_build_object(
                'custom_update_memory_prompt', 
                :prompt
            )
        )
        WHERE key = 'main'
        """)
        
        print(f"Updating with prompt length: {len(BETTER_UPDATE_MEMORY_PROMPT)}")
        print(f"Prompt handles empty memory: {'No existing memories' in BETTER_UPDATE_MEMORY_PROMPT}")
        
        # Execute the update
        db.execute(sql_update, {"prompt": BETTER_UPDATE_MEMORY_PROMPT})
        db.commit()
        
        print("✅ SQL update executed and committed")
        
        # Verify the update
        verification_result = db.execute(
            text("SELECT LENGTH(value->'mem0'->>'custom_update_memory_prompt') as prompt_length FROM memory_master.configs WHERE key = 'main'")
        ).fetchone()
        
        if verification_result:
            prompt_length = verification_result[0]
            print(f"Verification - New prompt length: {prompt_length}")
            return True
        else:
            print("❌ Verification failed")
            return False
        
    except Exception as e:
        print(f"❌ Error updating prompt: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = update_better_prompt()
    if success:
        print("🎉 Better evolution prompt updated successfully!")
    else:
        print("💥 Failed to update evolution prompt")
