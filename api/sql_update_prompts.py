#!/usr/bin/env python3
"""
SQL Update Evolution Prompts

Use raw SQL to update the evolution prompts.
"""

import sys
import os
import json

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app'))

from app.database import SessionL<PERSON>al
from sqlalchemy import text

# Fixed prompt with numeric IDs
FIXED_UPDATE_MEMORY_PROMPT = """
You are an expert technical memory evolution system. Your job is to intelligently manage a developer's technical memory by deciding how to handle new information against existing memories.

EVOLUTION OPERATIONS:
- ADD: Store completely new technical information
- UPDATE: Enhance or refine existing technical knowledge
- DELETE: Remove outdated or contradicted information
- NOOP: Ignore redundant or non-valuable information

TECHNICAL EVOLUTION RULES:

1. TECHNOLOGY MIGRATIONS:
   - "switched from X to Y" → DELETE X preference, ADD Y preference
   - "migrated from X to Y" → UPDATE to reflect migration
   - "no longer using X" → DELETE X if it was a preference

2. SKILL PROGRESSION:
   - "learning X" → "proficient in X" → UPDATE skill level
   - "beginner" → "intermediate" → "expert" → UPDATE progression
   - New certifications or achievements → ADD or UPDATE

3. PROJECT EVOLUTION:
   - "working on X" → "completed X" → UPDATE project status
   - New project started → ADD project information
   - Project cancelled → UPDATE or DELETE as appropriate

4. PREFERENCE CHANGES:
   - "prefers X over Y" when Y was previously preferred → DELETE Y preference, ADD X preference
   - Tool/framework preferences → UPDATE existing or ADD new

5. CAPABILITY UPDATES:
   - "doesn't know X" → "proficient in X" → UPDATE capability
   - New skills acquired → ADD new capabilities
   - Skills no longer relevant → DELETE if outdated

6. CONFLICT RESOLUTION:
   - Contradictory information → DELETE old, ADD new
   - Version updates → UPDATE to latest version
   - Deprecated technologies → UPDATE to mark as deprecated

DECISION CRITERIA:
- Prioritize recent, specific information over old, general information
- Maintain skill progression history when possible
- Preserve valuable context while updating facts
- Remove truly obsolete information to prevent confusion

OUTPUT FORMAT:
Return a JSON array of operations. Each operation should have:
- "id": existing memory ID (for UPDATE/DELETE) or null (for ADD)
- "text": the new or updated memory text
- "event": "ADD", "UPDATE", "DELETE", or "NOOP"
- "old_memory": existing memory text (for UPDATE/DELETE operations)

EXAMPLES:

Candidate Fact: "Now expert in Python after 3 years of development"
Existing Memory: "Learning Python programming language"
Output: [{
  "id": "0",
  "text": "Expert in Python programming language with 3 years of development experience",
  "event": "UPDATE",
  "old_memory": "Learning Python programming language"
}]

Candidate Fact: "Switched from MySQL to PostgreSQL for better JSON support"
Existing Memory: "Prefers MySQL database for web applications"
Output: [{
  "id": "1", 
  "text": "Prefers PostgreSQL database for better JSON support",
  "event": "UPDATE",
  "old_memory": "Prefers MySQL database for web applications"
}]

Now process the following candidate fact against existing memories:
""".strip()

def sql_update_prompts():
    """Use raw SQL to update the evolution prompts."""
    print("🔧 Using raw SQL to update evolution prompts...")
    
    db = SessionLocal()
    try:
        # First, get the current configuration
        result = db.execute(text("SELECT value FROM memory_master.configs WHERE key = 'main'")).fetchone()
        if not result:
            print("❌ No configuration found")
            return False

        current_config = result[0]
        print(f"Current config type: {type(current_config)}")

        # Use SQL to update the JSON field
        sql_update = text("""
        UPDATE memory_master.configs
        SET value = value::jsonb || jsonb_build_object(
            'mem0',
            (value::jsonb->'mem0') || jsonb_build_object(
                'custom_update_memory_prompt',
                :prompt
            )
        )
        WHERE key = 'main'
        """)

        print(f"Updating with prompt length: {len(FIXED_UPDATE_MEMORY_PROMPT)}")
        print(f"Prompt contains 'existing-memory-id': {'existing-memory-id' in FIXED_UPDATE_MEMORY_PROMPT}")

        # Execute the update
        db.execute(sql_update, {"prompt": FIXED_UPDATE_MEMORY_PROMPT})
        db.commit()

        print("✅ SQL update executed and committed")

        # Verify the update
        verification_result = db.execute(
            text("SELECT value->'mem0'->>'custom_update_memory_prompt' LIKE '%existing-memory-id%' as has_old_prompt FROM memory_master.configs WHERE key = 'main'")
        ).fetchone()
        
        if verification_result:
            has_old_prompt = verification_result[0]
            print(f"Verification - Has old prompt: {has_old_prompt}")
            return not has_old_prompt
        else:
            print("❌ Verification failed")
            return False
        
    except Exception as e:
        print(f"❌ Error updating prompts: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = sql_update_prompts()
    if success:
        print("🎉 Evolution prompts successfully updated with numeric IDs!")
    else:
        print("💥 Failed to update evolution prompts")
